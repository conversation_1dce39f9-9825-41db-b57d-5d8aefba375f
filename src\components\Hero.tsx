
import { Message<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Spark<PERSON>, <PERSON>, Zap } from "lucide-react";

const Hero = () => (
  <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 via-white to-emerald-50/30 overflow-hidden">
    {/* Background Elements */}
    <div className="absolute inset-0">
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-lia-green/5 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-emerald-300/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-lia-green/5 to-emerald-300/5 rounded-full blur-3xl"></div>
    </div>

    <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
      <div className="text-center space-y-8">
        {/* Badge */}
        <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/80 backdrop-blur-sm border border-lia-green/20 rounded-full shadow-lg">
          <Sparkles className="w-4 h-4 text-lia-green" />
          <span className="text-sm font-semibold text-gray-700">Assistente Financeira</span>
          <div className="w-2 h-2 bg-lia-green rounded-full animate-pulse"></div>
        </div>

        {/* Main Headline */}
        <div className="space-y-4">
          <h1 className="text-4xl sm:text-6xl lg:text-7xl font-black text-gray-900 leading-tight">
            Sua vida financeira
            <span className="block bg-gradient-to-r from-lia-green to-emerald-500 bg-clip-text text-transparent">
              organizada e simples
            </span>
            <span className="block text-gray-900">direto no WhatsApp</span>
          </h1>
        </div>

        {/* Subheadline */}
        <p className="text-xl sm:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-medium">
          Pare de se preocupar com bagunça, atrasos e falta de clareza.
          <br />
          <span className="text-lia-green font-bold">A Lia deixa o controle do seu dinheiro leve, simples e automático</span>.
          <br />
          Sinta agora o alívio e tranquilidade de ter tudo resolvido.
        </p>

        {/* Trust Indicators */}
        <div className="flex flex-wrap justify-center items-center gap-6 text-sm text-gray-500">
          <div className="flex items-center gap-2">
            <Shield className="w-4 h-4 text-lia-green" />
            <span>100% Seguro</span>
          </div>
          <div className="flex items-center gap-2">
            <Zap className="w-4 h-4 text-lia-green" />
            <span>Resposta Instantânea</span>
          </div>
          <div className="flex items-center gap-2">
            <MessageCircle className="w-4 h-4 text-lia-green" />
            <span>Direto no WhatsApp</span>
          </div>
        </div>

        {/* CTA Section */}
        <div className="space-y-6">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="#planos"
              className="group inline-flex items-center justify-center gap-3 px-8 py-4 bg-gradient-to-r from-lia-green to-emerald-500 text-white font-bold text-lg rounded-full shadow-xl hover:shadow-2xl hover:shadow-lia-green/25 transform hover:-translate-y-1 transition-all duration-300"
            >
              Quero organizar minha vida financeira!
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
            </a>
          </div>

          {/* Live Status */}
          <div className="flex items-center justify-center gap-3 text-gray-500">
            <div className="relative">
              <div className="w-3 h-3 bg-lia-green rounded-full"></div>
              <div className="absolute inset-0 w-3 h-3 bg-lia-green rounded-full animate-ping opacity-75"></div>
            </div>
            <span className="font-medium">
              Online agora • Comece em segundos • Experimente a paz financeira
            </span>
          </div>
        </div>

        {/* Social Proof Preview */}
        <div className="pt-12">
          <div className="inline-flex items-center gap-4 px-6 py-3 bg-white/60 backdrop-blur-sm rounded-full border border-gray-200 shadow-lg">
            <div className="flex -space-x-2">
              {[
                { src: "https://images.unsplash.com/photo-1594744803329-e58b31de8bf5?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D", alt: "Maria" },
                { src: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=64&h=64&fit=crop&crop=face", alt: "João" },
                { src: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=64&h=64&fit=crop&crop=face", alt: "Ana" },
                { src: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=64&h=64&fit=crop&crop=face", alt: "Carlos" }
              ].map((person, i) => (
                <img
                  key={i}
                  src={person.src}
                  alt={person.alt}
                  className="w-8 h-8 rounded-full border-2 border-white object-cover"
                />
              ))}
            </div>
            <span className="text-sm font-semibold text-gray-700">
              +500 pessoas já organizaram suas finanças
            </span>
          </div>
        </div>
      </div>
    </div>

    {/* Floating Elements */}
    <div className="absolute top-20 left-10 w-20 h-20 bg-lia-green/10 rounded-2xl rotate-12 animate-bounce delay-300"></div>
    <div className="absolute bottom-20 right-10 w-16 h-16 bg-emerald-300/10 rounded-full animate-bounce delay-700"></div>
  </section>
);

export default Hero;
