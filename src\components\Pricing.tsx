
import { DollarSign, Check, Star, Zap } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

const plans = [
  {
    title: "Mensal",
    price: "R$29,90",
    period: "por mês",
    highlight: false,
    popular: false,
    savings: null,
    features: [
      "Controle financeiro completo",
      "Lembretes automáticos",
      "Suporte 24/7 no WhatsApp",
      "Relatórios mensais"
    ]
  },
  {
    title: "Anual",
    price: "R$299,90",
    period: "por ano",
    highlight: true,
    popular: true,
    savings: "Economize 2 meses",
    features: [
      "Tudo do plano mensal",
      "2 meses grátis",
      "Insights avançados",
      "Suporte prioritário",
      "Metas personalizadas"
    ]
  },
];

const Pricing = () => (
  <section className="py-24 bg-gradient-to-br from-gray-50 to-emerald-50/30" id="planos">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="text-center max-w-4xl mx-auto mb-16">
        <div className="inline-flex items-center gap-2 px-4 py-2 bg-lia-green/10 rounded-full mb-6">
          <DollarSign className="w-4 h-4 text-lia-green" />
          <span className="text-sm font-semibold text-lia-green">PLANOS</span>
        </div>
        <h2 className="text-4xl sm:text-5xl font-black text-gray-900 mb-6 leading-tight">
          Invista pouco para conquistar
          <span className="block bg-gradient-to-r from-lia-green to-emerald-500 bg-clip-text text-transparent">
            sua liberdade financeira
          </span>
        </h2>
        <p className="text-xl text-gray-600 leading-relaxed mb-8">
          Controle e tranquilidade para o seu dinheiro na palma da mão. Escolha o plano, mude sua história.
        </p>
        <div className="inline-flex items-center gap-2 px-6 py-3 bg-lia-green/10 rounded-full">
          <Zap className="w-4 h-4 text-lia-green" />
          <span className="text-sm font-semibold text-lia-green">
            O próximo boleto pode ser só uma mensagem de WhatsApp de distância da tranquilidade
          </span>
        </div>
      </div>

      {/* Plans */}
      <div className="flex flex-col lg:flex-row gap-8 justify-center items-center lg:items-stretch max-w-4xl mx-auto">
        {plans.map((plan) => (
          <div key={plan.title} className="relative w-full max-w-sm">
            <Card className={`relative h-full ${
              plan.highlight
                ? "border-2 border-lia-green shadow-2xl shadow-lia-green/20 scale-105"
                : "border border-gray-200 shadow-lg"
            } transition-all duration-300 hover:shadow-2xl hover:-translate-y-2`}>

              {/* Popular Badge - agora dentro do card */}
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
                  <div className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-lia-green to-emerald-500 text-white text-sm font-bold rounded-full shadow-lg border-2 border-white">
                    <Star className="w-4 h-4" />
                    MAIS POPULAR
                  </div>
                </div>
              )}
              {/* Background Gradient for highlighted plan */}
              {plan.highlight && (
                <div className="absolute inset-0 bg-gradient-to-br from-lia-green/5 to-emerald-500/5 rounded-lg"></div>
              )}
              
              <CardHeader className={`relative text-center pb-8 ${plan.popular ? 'pt-12' : 'pt-6'}`}>
                <div className={`inline-flex items-center justify-center w-16 h-16 ${
                  plan.highlight 
                    ? "bg-gradient-to-br from-lia-green to-emerald-500" 
                    : "bg-gray-100"
                } rounded-2xl mx-auto mb-4 shadow-lg`}>
                  <DollarSign className={`w-8 h-8 ${plan.highlight ? "text-white" : "text-gray-600"}`} />
                </div>
                
                <CardTitle className="text-2xl font-bold text-gray-900 mb-2">
                  {plan.title}
                </CardTitle>
                
                {plan.savings && (
                  <div className="inline-flex items-center gap-1 px-3 py-1 bg-lia-green/10 text-lia-green text-sm font-bold rounded-full">
                    <Star className="w-3 h-3" />
                    {plan.savings}
                  </div>
                )}
              </CardHeader>
              
              <CardContent className="relative text-center pb-8">
                {/* Price */}
                <div className="mb-8">
                  <div className="flex items-baseline justify-center gap-2">
                    <span className="text-4xl font-black text-gray-900">
                      {plan.price}
                    </span>
                    <span className="text-gray-500 font-medium">
                      {plan.period}
                    </span>
                  </div>
                  {plan.title === "Anual" && (
                    <div className="text-sm text-gray-500 mt-2">
                      Equivale a R$ 24,99/mês
                    </div>
                  )}
                </div>

                {/* Features */}
                <div className="space-y-4 text-left">
                  {plan.features.map((feature, idx) => (
                    <div key={idx} className="flex items-center gap-3">
                      <div className="flex-shrink-0 w-5 h-5 bg-lia-green/10 rounded-full flex items-center justify-center">
                        <Check className="w-3 h-3 text-lia-green" />
                      </div>
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
              
              <CardFooter className="relative pt-0">
                <Button 
                  className={`w-full ${
                    plan.highlight
                      ? "bg-gradient-to-r from-lia-green to-emerald-500 hover:from-lia-green-dark hover:to-emerald-600 text-white shadow-xl hover:shadow-2xl"
                      : "bg-white border-2 border-lia-green text-lia-green hover:bg-lia-green hover:text-white"
                  } font-bold text-lg py-6 rounded-xl transition-all duration-300 transform hover:-translate-y-1`}
                  size="lg"
                >
                  {plan.highlight ? "Escolher Plano Premium" : "Começar Agora"}
                </Button>
              </CardFooter>
            </Card>
          </div>
        ))}
      </div>

      {/* Bottom Message */}
      <div className="text-center mt-16">
        <div className="inline-flex items-center gap-3 px-8 py-4 bg-white rounded-2xl shadow-lg border border-gray-100">
          <div className="w-12 h-12 bg-gradient-to-br from-lia-green to-emerald-500 rounded-xl flex items-center justify-center">
            <Zap className="w-6 h-6 text-white" />
          </div>
          <div className="text-left">
            <div className="font-bold text-gray-900">
              Chegou a sua vez de viver em paz com seu dinheiro
            </div>
            <div className="text-gray-600 text-sm">
              Experimente a Lia e transforme sua vida financeira hoje mesmo!
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
);

export default Pricing;
