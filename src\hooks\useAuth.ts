import { useState, useEffect } from 'react'
import { supabase, type AuthUser } from '@/lib/supabase'
import { User } from '@supabase/supabase-js'

export const useAuth = () => {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(true)
  const [userDataLoaded, setUserDataLoaded] = useState(false)

  // Função para salvar dados do usuário no localStorage
  const saveUserToStorage = (userData: AuthUser) => {
    try {
      localStorage.setItem('lia_user_data', JSON.stringify(userData))
    } catch (error) {
      console.warn('Erro ao salvar dados do usuário no localStorage:', error)
    }
  }

  // Função para carregar dados do usuário do localStorage
  const loadUserFromStorage = (): AuthUser | null => {
    try {
      const stored = localStorage.getItem('lia_user_data')
      return stored ? JSON.parse(stored) : null
    } catch (error) {
      console.warn('Erro ao carregar dados do usuário do localStorage:', error)
      return null
    }
  }

  // Função para limpar dados do localStorage
  const clearUserFromStorage = () => {
    try {
      localStorage.removeItem('lia_user_data')
    } catch (error) {
      console.warn('Erro ao limpar dados do usuário do localStorage:', error)
    }
  }

  useEffect(() => {
    // Carregar dados do localStorage primeiro
    const storedUser = loadUserFromStorage()
    if (storedUser) {
      setUser(storedUser)
      setUserDataLoaded(true)
    }

    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()
        if (session?.user) {
          await loadUserData(session.user)
        } else {
          // Se não há sessão, limpar dados armazenados
          setUser(null)
          clearUserFromStorage()
        }
      } catch (error) {
        setUser(null)
        clearUserFromStorage()
      } finally {
        setLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (_, session) => {
      try {
        if (session?.user) {
          await loadUserData(session.user)
        } else {
          setUser(null)
          clearUserFromStorage()
          setUserDataLoaded(false)
        }
      } catch (error) {
        setUser(null)
        clearUserFromStorage()
      } finally {
        setLoading(false)
      }
    })

    return () => subscription.unsubscribe()
  }, [])

  const loadUserData = async (authUser: User) => {
    // Se já carregamos os dados completos para este usuário, não buscar novamente
    if (userDataLoaded && user && user.id === authUser.id && user.nome && user.numero) {
      return
    }

    // Verificar se temos dados no localStorage para este usuário
    const storedUser = loadUserFromStorage()
    if (storedUser && storedUser.id === authUser.id && storedUser.nome && storedUser.numero) {
      // Usar dados do localStorage se estão completos
      setUser(storedUser)
      setUserDataLoaded(true)
      return
    }

    // Se não temos dados completos, buscar do banco
    // Mas não sobrescrever dados existentes com dados incompletos
    if (!user || user.id !== authUser.id) {
      // Só definir dados básicos se não temos nenhum dado
      const basicUser = {
        id: authUser.id,
        email: authUser.email || ''
      }
      setUser(basicUser)
      setUserDataLoaded(false)
    }

    try {
      // Add timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Timeout loading user data')), 3000)
      )

      const queryPromise = supabase
        .from('users')
        .select('numero, nome')
        .eq('auth_uuid', authUser.id)
        .single()

      const { data: userData, error } = await Promise.race([queryPromise, timeoutPromise]) as any

      if (error) {
        // Se já temos dados completos, não sobrescrever com dados básicos
        if (user && user.nome && user.numero) {
          return
        }
        console.warn('Erro ao carregar dados do usuário:', error)
        return
      }

      // Atualizar com dados completos e salvar no localStorage
      const completeUser = {
        id: authUser.id,
        email: authUser.email || '',
        numero: userData?.numero,
        nome: userData?.nome
      }

      setUser(completeUser)
      setUserDataLoaded(true)
      saveUserToStorage(completeUser)

    } catch (error) {
      // Se já temos dados completos, não sobrescrever
      if (user && user.nome && user.numero) {
        return
      }
      console.warn('Erro inesperado ao carregar dados do usuário:', error)
    }
  }

  const signInWithNumber = async (numero: number, password: string) => {
    try {
      const phoneNumber = numero.toString()

      // Get email by phone
      const { data: emailData, error: emailError } = await supabase
        .rpc('get_user_email_by_phone', { user_phone: phoneNumber })

      if (emailError || !emailData) {
        throw new Error('Número não encontrado')
      }

      // Sign in
      const { data, error } = await supabase.auth.signInWithPassword({
        email: emailData,
        password: password
      })

      if (error) {
        throw new Error('Credenciais inválidas')
      }

      return { data, error: null }
    } catch (error: any) {
      return { data: null, error }
    }
  }

  const signOut = async () => {
    const { error } = await supabase.auth.signOut()
    setUser(null)
    setUserDataLoaded(false)
    clearUserFromStorage()
    return { error }
  }

  return {
    user,
    loading,
    signInWithNumber,
    signOut
  }
}
