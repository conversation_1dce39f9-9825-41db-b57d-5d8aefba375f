
const PlanoIncidentes = () => (
  <div className="min-h-screen bg-white">
    <div className="max-w-4xl mx-auto px-4 py-16">
      <h1 className="text-3xl font-bold text-black mb-8">PLANO DE RESPOSTA A INCIDENTES DE DADOS</h1>
      <p className="text-lg text-gray-600 mb-8">Objetivo: Definir ações para resposta imediata e mitigação de incidentes.</p>
      
      <div className="space-y-8 text-gray-700">
        <section>
          <h2 className="text-xl font-bold text-black mb-4">Equipe</h2>
          <p><strong>Controlador:</strong> <PERSON><PERSON></p>
          <p><strong>Operadores:</strong> OpenAI, Supabase, Z-API, n8n</p>
        </section>

        <section>
          <h2 className="text-xl font-bold text-black mb-4">Classificação de Incidentes</h2>
          <ul className="list-disc ml-6 space-y-1">
            <li><strong>Nível 1:</strong> Falha pontual</li>
            <li><strong>Nível 2:</strong> Vazamento controlado</li>
            <li><strong>Nível 3:</strong> Vazamento massivo</li>
          </ul>
        </section>

        <section>
          <h2 className="text-xl font-bold text-black mb-4">Ações</h2>
          <ol className="list-decimal ml-6 space-y-1">
            <li>Conter e isolar o incidente</li>
            <li>Comunicar os envolvidos</li>
            <li>Registrar logs e evidências</li>
            <li>Notificar ANPD (nível 2 ou 3) em até 2 dias úteis</li>
            <li>Informar usuários afetados via e-mail e/ou WhatsApp</li>
          </ol>
        </section>

        <section>
          <h2 className="text-xl font-bold text-black mb-4">Registro</h2>
          <p>Manter histórico de incidentes.</p>
        </section>

        <section>
          <h2 className="text-xl font-bold text-black mb-4">Revisão</h2>
          <p>Analisar causas e revisar controles após cada incidente.</p>
        </section>
      </div>
    </div>
  </div>
);

export default PlanoIncidentes;
