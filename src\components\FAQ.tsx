
import { Plus, Minus, HelpCircle } from "lucide-react";
import { useState } from "react";

const faqs = [
  {
    q: "Preciso instalar algo para usar a Lia?",
    a: "Nada! É só começar a conversar com a Lia diretamente pelo WhatsApp. Não precisa baixar apps extras ou criar contas complicadas. Simples assim!"
  },
  {
    q: "A Lia responde imediatamente?",
    a: "Sim! A Lia está disponível 24h por dia, 7 dias por semana para responder em segundos. Nossa IA é otimizada para dar respostas rápidas e precisas sobre suas finanças."
  },
  {
    q: "É possível cancelar quando quiser?",
    a: "Sim, o cancelamento é fácil e sem burocracia — você faz tudo online e em poucos cliques. Não há multas, taxas de cancelamento ou pegadinhas."
  },
  {
    q: "Minhas conversas ficam privadas?",
    a: "Totalmente! Sua privacidade é nossa prioridade máxima. Todos os dados são criptografados e seguimos rigorosamente a LGPD. Suas informações financeiras ficam 100% seguras."
  },
  {
    q: "Como a Lia entende minhas informações financeiras?",
    a: "A Lia usa inteligência artificial avançada para entender texto, imagens e áudios. Você pode enviar fotos de recibos, falar seus gastos ou simplesmente digitar - ela entende tudo!"
  },
  {
    q: "Posso usar a Lia para controlar gastos da família?",
    a: "Por enquanto, a Lia funciona melhor para controle individual. Mas você pode compartilhar relatórios e insights com sua família facilmente através do WhatsApp."
  }
];

const FAQ = () => {
  const [openItems, setOpenItems] = useState<number[]>([]);

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  return (
    <section className="py-24 bg-gradient-to-br from-gray-50 to-emerald-50/30" id="faq">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-lia-green/10 rounded-full mb-6">
            <HelpCircle className="w-4 h-4 text-lia-green" />
            <span className="text-sm font-semibold text-lia-green">FAQ</span>
          </div>
          <h2 className="text-4xl sm:text-5xl font-black text-gray-900 mb-6">
            Perguntas
            <span className="block bg-gradient-to-r from-lia-green to-emerald-500 bg-clip-text text-transparent">
              Frequentes
            </span>
          </h2>
          <p className="text-xl text-gray-600 leading-relaxed">
            Tire suas dúvidas sobre a Lia e comece hoje mesmo
          </p>
        </div>

        {/* FAQ Items */}
        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <div 
              key={index} 
              className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-300"
            >
              <button
                onClick={() => toggleItem(index)}
                className="w-full flex items-center justify-between p-6 text-left hover:bg-gray-50/50 transition-colors duration-300 group"
              >
                <h3 className="text-lg font-bold text-gray-900 group-hover:text-lia-green transition-colors duration-300 pr-4">
                  {faq.q}
                </h3>
                <div className="flex-shrink-0 w-8 h-8 bg-lia-green/10 rounded-full flex items-center justify-center group-hover:bg-lia-green group-hover:text-white transition-all duration-300">
                  {openItems.includes(index) ? (
                    <Minus className="w-4 h-4 text-lia-green group-hover:text-white" />
                  ) : (
                    <Plus className="w-4 h-4 text-lia-green group-hover:text-white" />
                  )}
                </div>
              </button>
              
              {openItems.includes(index) && (
                <div className="px-6 pb-6 animate-fade-in-lia">
                  <div className="pt-2 border-t border-gray-100">
                    <p className="text-gray-600 leading-relaxed">
                      {faq.a}
                    </p>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Ainda tem dúvidas?
            </h3>
            <p className="text-gray-600 mb-6">
              Fale diretamente com nossa equipe pelo WhatsApp
            </p>
            <a
              href="https://wa.me/5517998761086"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-lia-green to-emerald-500 text-white font-bold rounded-full shadow-xl hover:shadow-2xl hover:-translate-y-1 transition-all duration-300"
            >
              Falar no WhatsApp
              <HelpCircle className="w-5 h-5" />
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQ;
