
const DPA = () => (
  <div className="min-h-screen bg-white">
    <div className="max-w-4xl mx-auto px-4 py-16">
      <h1 className="text-3xl font-bold text-black mb-8">CONTRATO DE PROCESSAMENTO DE DADOS (DPA)</h1>
      
      <div className="bg-gray-50 p-6 rounded-lg mb-8">
        <p><strong>Entre:</strong> <PERSON><PERSON> (Controlador)</p>
        <p><strong>E:</strong> [Nome da ferramenta ou serviço] (Operador de Dados)</p>
        <p><strong>Objeto:</strong> Regular o tratamento de dados pessoais realizado pelo Operador em nome do Controlador.</p>
      </div>
      
      <div className="space-y-8 text-gray-700">
        <section>
          <h2 className="text-xl font-bold text-black mb-4">1. Obrigações do Operador</h2>
          <ul className="list-disc ml-6 space-y-1">
            <li>Tratar dados somente conforme instruções do Controlador</li>
            <li>Adotar medidas de segurança compatíveis</li>
            <li>Notificar incidentes de segurança imediatamente</li>
            <li>Permitir auditoria quando requisitado</li>
          </ul>
        </section>

        <section>
          <h2 className="text-xl font-bold text-black mb-4">2. Tipos de Dados</h2>
          <p>Dados pessoais e sensíveis (financeiros e multimídia)</p>
        </section>

        <section>
          <h2 className="text-xl font-bold text-black mb-4">3. Finalidade</h2>
          <p>Prestação de serviços de processamento, armazenamento, integração ou análise de dados.</p>
        </section>

        <section>
          <h2 className="text-xl font-bold text-black mb-4">4. Subcontratação</h2>
          <p>Permitida apenas com autorização prévia do Controlador.</p>
        </section>

        <section>
          <h2 className="text-xl font-bold text-black mb-4">5. Vigência</h2>
          <p>Enquanto houver relação de tratamento de dados.</p>
        </section>
      </div>
    </div>
  </div>
);

export default DPA;
