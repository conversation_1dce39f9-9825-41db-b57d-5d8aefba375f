# Lia Digital Oasis

## Project info

Lia é uma assistente financeira inteligente que funciona diretamente pelo WhatsApp, ajudando você a organizar suas finanças de forma simples e eficiente.

## How can I edit this code?

**Use your preferred IDE**

You can clone this repo and push changes to work locally with your preferred development environment.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

You can deploy this project using various hosting platforms like Vercel, Netlify, or any other static hosting service that supports React applications.

For Vercel:
1. Connect your GitHub repository to Vercel
2. Configure the build settings (build command: `npm run build`, output directory: `dist`)
3. Deploy

For Netlify:
1. Connect your GitHub repository to Netlify
2. Set build command to `npm run build` and publish directory to `dist`
3. Deploy
