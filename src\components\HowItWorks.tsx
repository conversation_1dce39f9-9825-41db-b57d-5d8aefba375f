
import { Smartphone, Zap, Target, ArrowRight } from "lucide-react";

const steps = [
  {
    icon: <Smartphone className="w-8 h-8" />,
    title: "Chame a Lia no WhatsApp",
    desc: "Sem baixar apps ou criar cadastros complicados. Basta mandar um 'oi' e começar a organizar sua vida financeira agora mesmo.",
    color: "from-blue-500 to-blue-600"
  },
  {
    icon: <Zap className="w-8 h-8" />,
    title: "Compartilhe seus gastos e contas",
    desc: "A Lia entende e registra automaticamente tudo, trazendo clareza de onde vai seu dinheiro.",
    color: "from-lia-green to-emerald-500"
  },
  {
    icon: <Target className="w-8 h-8" />,
    title: "Receba insights e lembretes práticos",
    desc: "Metas, alertas de contas e dicas sem te incomodar. Tenha uma rotina mais leve, sob controle e tranquila.",
    color: "from-purple-500 to-purple-600"
  },
];

const HowItWorks = () => (
  <section id="como-funciona" className="py-24 bg-white">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="text-center max-w-3xl mx-auto mb-16">
        <div className="inline-flex items-center gap-2 px-4 py-2 bg-lia-green/10 rounded-full mb-6">
          <Zap className="w-4 h-4 text-lia-green" />
          <span className="text-sm font-semibold text-lia-green">COMO FUNCIONA</span>
        </div>
        <h2 className="text-4xl sm:text-5xl font-black text-gray-900 mb-6">
          Como a Lia transforma
          <span className="block bg-gradient-to-r from-lia-green to-emerald-500 bg-clip-text text-transparent">
            sua rotina financeira
          </span>
        </h2>
        <p className="text-xl text-gray-600 leading-relaxed">
          Em apenas 3 passos simples, você terá o controle total das suas finanças
        </p>
      </div>

      {/* Steps */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12">
        {steps.map((step, idx) => (
          <div key={step.title} className="relative group">
            {/* Card */}
            <div className="relative bg-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 group-hover:border-lia-green/20 min-h-[320px] flex flex-col">
              {/* Icon */}
              <div className={`inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br ${step.color} text-white rounded-2xl mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                {step.icon}
              </div>

              {/* Content */}
              <div className="flex-1">
                <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-lia-green transition-colors duration-300">
                  {step.title}
                </h3>
                <p className="text-gray-600 leading-relaxed mb-6">
                  {step.desc}
                </p>
              </div>

              {/* Arrow for desktop */}
              {idx < steps.length - 1 && (
                <div className="hidden lg:block absolute top-1/2 -right-6 transform -translate-y-1/2">
                  <ArrowRight className="w-6 h-6 text-gray-300 group-hover:text-lia-green transition-colors duration-300" />
                </div>
              )}
            </div>

            {/* Step Number - positioned in front of the card */}
            <div className="absolute -top-4 -left-4 w-10 h-10 bg-gradient-to-r from-lia-green to-emerald-500 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg z-10">
              {idx + 1}
            </div>
          </div>
        ))}
      </div>

      {/* Bottom CTA */}
      <div className="text-center mt-16">
        <a
          href="#planos"
          className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-lia-green to-emerald-500 text-white font-bold rounded-full shadow-xl hover:shadow-2xl hover:-translate-y-1 transition-all duration-300"
        >
          Começar minha transformação financeira
          <ArrowRight className="w-5 h-5" />
        </a>
      </div>
    </div>
  </section>
);

export default HowItWorks;
