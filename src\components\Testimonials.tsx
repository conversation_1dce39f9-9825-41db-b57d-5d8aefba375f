
import { Star, Quote } from "lucide-react";

const testimonials = [
  {
    name: "<PERSON>",
    role: "<PERSON><PERSON><PERSON><PERSON>",
    text: "A Lia mudou minha rotina! Tudo mais organizado e rápido, sem precisar sair do WhatsApp. Finalmente tenho controle real das minhas finanças.",
    rating: 5,
    avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=96&h=96&fit=crop&crop=face"
  },
  {
    name: "<PERSON>",
    role: "<PERSON><PERSON>",
    text: "Incrível como a Lia resolve tarefas do dia a dia e responde tudo rapidinho. Nunca mais esqueço de pagar uma conta. Recomendo muito!",
    rating: 5,
    avatar: "https://images.unsplash.com/photo-1594744803329-e58b31de8bf5?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D  "
  },
  {
    name: "<PERSON><PERSON>",
    role: "Consultor",
    text: "Assistente inteligente, fácil de usar e suporte excelente. Vale cada centavo! Minha vida financeira nunca esteve tão organizada.",
    rating: 5,
    avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=96&h=96&fit=crop&crop=face"
  }
];

const Testimonials = () => (
  <section id="depoimentos" className="py-24 bg-white">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="text-center max-w-3xl mx-auto mb-16">
        <div className="inline-flex items-center gap-2 px-4 py-2 bg-lia-green/10 rounded-full mb-6">
          <Star className="w-4 h-4 text-lia-green" />
          <span className="text-sm font-semibold text-lia-green">DEPOIMENTOS</span>
        </div>
        <h2 className="text-4xl sm:text-5xl font-black text-gray-900 mb-6">
          O que dizem nossos
          <span className="block bg-gradient-to-r from-lia-green to-emerald-500 bg-clip-text text-transparent">
            usuários satisfeitos
          </span>
        </h2>
        <p className="text-xl text-gray-600 leading-relaxed">
          Histórias reais de pessoas que transformaram suas vidas financeiras
        </p>
      </div>

      {/* Testimonials Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {testimonials.map((testimonial, idx) => (
          <div key={idx} className="group">
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 hover:border-lia-green/20 h-full relative">
              {/* Quote Icon */}
              <div className="absolute -top-4 -left-4 w-8 h-8 bg-gradient-to-br from-lia-green to-emerald-500 rounded-full flex items-center justify-center shadow-lg">
                <Quote className="w-4 h-4 text-white" />
              </div>

              {/* Rating */}
              <div className="flex items-center gap-1 mb-6">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>

              {/* Content */}
              <blockquote className="text-gray-700 leading-relaxed mb-6 italic">
                "{testimonial.text}"
              </blockquote>

              {/* Author */}
              <div className="flex items-center gap-4">
                <img
                  src={testimonial.avatar}
                  alt={testimonial.name}
                  className="w-12 h-12 rounded-full object-cover shadow-lg border-2 border-gray-100"
                />
                <div>
                  <div className="font-bold text-gray-900 group-hover:text-lia-green transition-colors duration-300">
                    {testimonial.name}
                  </div>
                  <div className="text-gray-600 text-sm">
                    {testimonial.role}
                  </div>
                </div>
              </div>

              {/* Hover Effect */}
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-lia-green to-emerald-500 opacity-0 group-hover:opacity-5 transition-opacity duration-300"></div>
            </div>
          </div>
        ))}
      </div>

      {/* Trust Banner */}
      <div className="mt-16 bg-gradient-to-r from-lia-green to-emerald-500 rounded-3xl p-8 lg:p-12 text-white text-center">
        <div className="max-w-3xl mx-auto">
          <h3 className="text-2xl lg:text-3xl font-bold mb-4">
            Junte-se a centenas de pessoas que já transformaram suas finanças
          </h3>
          <p className="text-white/90 text-lg mb-8">
            Não deixe para depois. Sua paz financeira começa hoje.
          </p>
          <a
            href="#planos"
            className="inline-flex items-center gap-3 px-8 py-4 bg-white text-lia-green font-bold rounded-full shadow-xl hover:shadow-2xl hover:-translate-y-1 transition-all duration-300"
          >
            Começar minha transformação
            <Star className="w-5 h-5" />
          </a>
        </div>
      </div>
    </div>
  </section>
);

export default Testimonials;
