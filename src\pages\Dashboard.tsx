
import { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuthContext } from "@/contexts/AuthContext";
import { useFinancialData } from "@/hooks/useFinancialData";
import {
  MessageCircle,
  User,
  Settings,
  LogOut,
  PieChart,
  TrendingUp,
  DollarSign,
  Calendar,
  Bell,
  Search,
  Wallet,
  ArrowUpCircle,
  ArrowDownCircle,
  Home,
  BarChart3,
  Target,
  CreditCard,
  Menu,
  X
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  LineChart,
  Line,
  Legend
} from 'recharts';

const Dashboard = () => {
  const { user, signOut } = useAuthContext();
  const navigate = useNavigate();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeSection, setActiveSection] = useState('dashboard');

  const {
    loading,
    error,
    totalSaldo,
    totalEntradas,
    totalGastos,
    monthlyTotalEntradas,
    monthlyTotalGastos,
    recentEntradas,
    recentGastos,
    saldos,
    entradas,
    gastos
  } = useFinancialData();

  const handleLogout = async () => {
    await signOut();
    navigate("/");
  };

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  // Sidebar menu items
  const sidebarItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: Home,
      active: activeSection === 'dashboard'
    },
    {
      id: 'analytics',
      label: 'Análises',
      icon: BarChart3,
      active: activeSection === 'analytics'
    },
    {
      id: 'transactions',
      label: 'Transações',
      icon: CreditCard,
      active: activeSection === 'transactions'
    },
    {
      id: 'goals',
      label: 'Metas',
      icon: Target,
      active: activeSection === 'goals'
    },
    {
      id: 'account',
      label: 'Conta',
      icon: User,
      active: activeSection === 'account'
    }
  ];

  // Prepare chart data
  const pieChartData = [
    { name: 'Receitas', value: monthlyTotalEntradas, color: '#29d883' },
    { name: 'Despesas', value: monthlyTotalGastos, color: '#ef4444' }
  ];

  // Monthly comparison data (last 6 months)
  const getMonthlyData = () => {
    const months = [];
    const currentDate = new Date();

    for (let i = 5; i >= 0; i--) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
      const monthName = date.toLocaleDateString('pt-BR', { month: 'short' });
      const year = date.getFullYear();
      const month = date.getMonth() + 1;

      const monthEntradas = entradas.filter(entrada => {
        const entradaDate = new Date(entrada.data_entrada);
        return entradaDate.getMonth() + 1 === month && entradaDate.getFullYear() === year;
      }).reduce((sum, entrada) => sum + entrada.valor_entrada, 0);

      const monthGastos = gastos.filter(gasto => {
        const gastoDate = new Date(gasto.data_gasto);
        return gastoDate.getMonth() + 1 === month && gastoDate.getFullYear() === year;
      }).reduce((sum, gasto) => sum + gasto.valor_gasto, 0);

      months.push({
        month: monthName,
        receitas: monthEntradas,
        despesas: monthGastos,
        saldo: monthEntradas - monthGastos
      });
    }

    return months;
  };

  const monthlyData = getMonthlyData();

  // Show simple error message if there's an error
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-8 text-center">
          <h2 className="text-xl font-bold text-gray-900 mb-4">
            Erro ao carregar dados
          </h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <Button
            onClick={() => window.location.reload()}
            className="w-full bg-lia-green hover:bg-emerald-600"
          >
            Recarregar Página
          </Button>
        </div>
      </div>
    );
  }

  const stats = [
    {
      title: "Receitas do Mês",
      value: formatCurrency(monthlyTotalEntradas),
      change: loading ? "..." : `${recentEntradas.length} entradas`,
      icon: TrendingUp,
      color: "text-green-600"
    },
    {
      title: "Despesas do Mês",
      value: formatCurrency(monthlyTotalGastos),
      change: loading ? "..." : `${recentGastos.length} gastos`,
      icon: DollarSign,
      color: "text-red-600"
    },
    {
      title: "Saldo Total",
      value: formatCurrency(totalSaldo),
      change: loading ? "..." : `${saldos.length} contas`,
      icon: PieChart,
      color: "text-blue-600"
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'} transition-transform duration-300 ease-in-out lg:translate-x-0 lg:relative lg:transform-none`}>
        <div className="flex flex-col h-screen">
          {/* Logo */}
          <div className="flex items-center gap-3 p-6 border-b border-gray-200 flex-shrink-0">
            <div className="relative">
              <div className="absolute inset-0 bg-lia-green/20 rounded-full blur-sm"></div>
              <MessageCircle className="relative w-8 h-8 text-lia-green" />
            </div>
            <span className="text-xl font-black tracking-tight text-gray-900">
              LIA
            </span>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            {sidebarItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setActiveSection(item.id)}
                className={`w-full flex items-center gap-3 px-4 py-3 rounded-lg text-left transition-all duration-200 ${
                  item.active
                    ? 'bg-lia-green text-white shadow-lg'
                    : 'text-gray-600 hover:bg-gray-100 hover:text-lia-green'
                }`}
              >
                <item.icon className="w-5 h-5" />
                <span className="font-medium">{item.label}</span>
              </button>
            ))}
          </nav>

          {/* User Info & Logout */}
          <div className="p-4 border-t border-gray-200 flex-shrink-0">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-lia-green/10 rounded-full flex items-center justify-center">
                <User className="w-5 h-5 text-lia-green" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {user?.nome || (user?.numero ? `Usuário #${user.numero}` : 'Usuário')}
                </p>
                <p className="text-xs text-gray-500 truncate">{user?.email}</p>
              </div>
            </div>
            <Button
              onClick={handleLogout}
              variant="outline"
              className="w-full justify-start gap-2"
            >
              <LogOut className="w-4 h-4" />
              Sair
            </Button>
          </div>
        </div>
      </div>

      {/* Overlay for mobile */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-h-screen lg:ml-0">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200 flex-shrink-0">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              {/* Mobile menu button */}
              <Button
                variant="ghost"
                size="icon"
                className="lg:hidden"
                onClick={() => setSidebarOpen(true)}
              >
                <Menu className="h-5 w-5" />
              </Button>

              {/* Search */}
              <div className="flex-1 max-w-md mx-4 lg:mx-8">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Buscar transações..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lia-green focus:border-lia-green"
                  />
                </div>
              </div>

              {/* Notifications */}
              <Button variant="ghost" size="icon">
                <Bell className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 px-4 sm:px-6 lg:px-8 py-8 overflow-y-auto">
          {activeSection === 'dashboard' && (
            <>
              {/* Welcome Section */}
              <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  Bem-vindo de volta, {user?.nome || (user?.numero ? `#${user.numero}` : 'usuário')}! 👋
                </h1>
                <p className="text-gray-600">
                  Aqui está um resumo das suas finanças hoje.
                </p>
              </div>

              {/* Stats Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                {stats.map((stat, index) => (
                  <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                        <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                        <p className={`text-sm font-medium ${stat.color}`}>{stat.change}</p>
                      </div>
                      <div className={`p-3 rounded-full bg-gray-50`}>
                        <stat.icon className={`h-6 w-6 ${stat.color}`} />
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Charts Section */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                {/* Pie Chart - Receitas vs Despesas */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-6">Receitas vs Despesas (Este Mês)</h3>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <RechartsPieChart>
                        <Pie
                          data={pieChartData}
                          cx="50%"
                          cy="50%"
                          innerRadius={60}
                          outerRadius={100}
                          paddingAngle={5}
                          dataKey="value"
                        >
                          {pieChartData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                        <Legend />
                      </RechartsPieChart>
                    </ResponsiveContainer>
                  </div>
                </div>

                {/* Bar Chart - Últimos 6 meses */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-6">Evolução Mensal</h3>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={monthlyData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis tickFormatter={(value) => `R$ ${value}`} />
                        <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                        <Legend />
                        <Bar dataKey="receitas" fill="#29d883" name="Receitas" />
                        <Bar dataKey="despesas" fill="#ef4444" name="Despesas" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                {/* Recent Transactions */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-lg font-semibold text-gray-900">Transações Recentes</h2>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setActiveSection('transactions')}
                    >
                      Ver Todas
                    </Button>
                  </div>
                  <div className="space-y-4">
                    {loading ? (
                      <div className="text-center py-4">
                        <p className="text-gray-500">Carregando transações...</p>
                      </div>
                    ) : error ? (
                      <div className="text-center py-4">
                        <p className="text-red-500">Erro ao carregar dados: {error}</p>
                      </div>
                    ) : (
                      <>
                        {/* Recent Entradas */}
                        {recentEntradas.map((entrada) => (
                          <div key={`entrada-${entrada.id}`} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50">
                            <div className="flex items-center gap-3">
                              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                <TrendingUp className="h-5 w-5 text-green-600" />
                              </div>
                              <div>
                                <p className="font-medium text-gray-900">{entrada.nome_entrada}</p>
                                <p className="text-sm text-gray-500">Entrada • {entrada.data_entrada}</p>
                              </div>
                            </div>
                            <p className="font-semibold text-green-600">
                              +{formatCurrency(entrada.valor_entrada)}
                            </p>
                          </div>
                        ))}

                        {/* Recent Gastos */}
                        {recentGastos.map((gasto) => (
                          <div key={`gasto-${gasto.id}`} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50">
                            <div className="flex items-center gap-3">
                              <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                                <DollarSign className="h-5 w-5 text-red-600" />
                              </div>
                              <div>
                                <p className="font-medium text-gray-900">{gasto.nome_gasto}</p>
                                <p className="text-sm text-gray-500">Gasto • {gasto.data_gasto}</p>
                              </div>
                            </div>
                            <p className="font-semibold text-red-600">
                              -{formatCurrency(gasto.valor_gasto)}
                            </p>
                          </div>
                        ))}

                        {recentEntradas.length === 0 && recentGastos.length === 0 && (
                          <div className="text-center py-8">
                            <p className="text-gray-500">Nenhuma transação encontrada</p>
                            <p className="text-sm text-gray-400 mt-1">Suas transações aparecerão aqui</p>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h2 className="text-lg font-semibold text-gray-900 mb-6">Ações Rápidas</h2>
                  <div className="grid grid-cols-2 gap-4">
                    <Button className="h-20 flex-col gap-2 bg-gradient-to-r from-lia-green to-emerald-500 hover:from-emerald-600 hover:to-lia-green">
                      <Wallet className="h-5 w-5" />
                      Novo Saldo
                    </Button>
                    <Button variant="outline" className="h-20 flex-col gap-2">
                      <ArrowUpCircle className="h-5 w-5" />
                      Nova Entrada
                    </Button>
                    <Button variant="outline" className="h-20 flex-col gap-2">
                      <ArrowDownCircle className="h-5 w-5" />
                      Novo Gasto
                    </Button>
                    <Button variant="outline" className="h-20 flex-col gap-2">
                      <Bell className="h-5 w-5" />
                      Novo Lembrete
                    </Button>
                  </div>
                </div>
              </div>

              {/* Chat with LIA */}
              <div className="bg-gradient-to-r from-lia-green to-emerald-500 rounded-xl p-6 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-xl font-semibold mb-2">Converse com a LIA</h2>
                    <p className="text-green-100">
                      Tire suas dúvidas financeiras e receba conselhos personalizados
                    </p>
                  </div>
                  <Button variant="secondary" className="bg-white text-lia-green hover:bg-gray-100">
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Iniciar Chat
                  </Button>
                </div>
              </div>
            </>
          )}

          {/* Analytics Section */}
          {activeSection === 'analytics' && (
            <div className="space-y-8">
              <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">Análises Financeiras</h1>
                <p className="text-gray-600">Visualize seus dados financeiros em detalhes</p>
              </div>

              {/* Detailed Charts */}
              <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
                {/* Line Chart - Evolução do Saldo */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-6">Evolução do Saldo</h3>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={monthlyData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis tickFormatter={(value) => `R$ ${value}`} />
                        <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                        <Legend />
                        <Line
                          type="monotone"
                          dataKey="saldo"
                          stroke="#29d883"
                          strokeWidth={3}
                          name="Saldo Mensal"
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </div>

                {/* Detailed Pie Chart */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-6">Distribuição Financeira</h3>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <RechartsPieChart>
                        <Pie
                          data={pieChartData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                          outerRadius={120}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {pieChartData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                      </RechartsPieChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </div>

              {/* Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <div className="flex items-center gap-3 mb-2">
                    <TrendingUp className="h-5 w-5 text-green-600" />
                    <span className="text-sm font-medium text-gray-600">Total Receitas</span>
                  </div>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(totalEntradas)}</p>
                </div>
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <div className="flex items-center gap-3 mb-2">
                    <DollarSign className="h-5 w-5 text-red-600" />
                    <span className="text-sm font-medium text-gray-600">Total Despesas</span>
                  </div>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(totalGastos)}</p>
                </div>
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <div className="flex items-center gap-3 mb-2">
                    <PieChart className="h-5 w-5 text-blue-600" />
                    <span className="text-sm font-medium text-gray-600">Saldo Total</span>
                  </div>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(totalSaldo)}</p>
                </div>
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <div className="flex items-center gap-3 mb-2">
                    <Wallet className="h-5 w-5 text-purple-600" />
                    <span className="text-sm font-medium text-gray-600">Contas</span>
                  </div>
                  <p className="text-2xl font-bold text-gray-900">{saldos.length}</p>
                </div>
              </div>
            </div>
          )}

          {/* Transactions Section */}
          {activeSection === 'transactions' && (
            <div className="space-y-8">
              <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">Todas as Transações</h1>
                <p className="text-gray-600">Histórico completo de entradas e gastos</p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* All Entradas */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-6">Todas as Receitas</h3>
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {entradas.map((entrada) => (
                      <div key={`entrada-${entrada.id}`} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                            <TrendingUp className="h-5 w-5 text-green-600" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">{entrada.nome_entrada}</p>
                            <p className="text-sm text-gray-500">{entrada.data_entrada}</p>
                          </div>
                        </div>
                        <p className="font-semibold text-green-600">
                          +{formatCurrency(entrada.valor_entrada)}
                        </p>
                      </div>
                    ))}
                    {entradas.length === 0 && (
                      <div className="text-center py-8">
                        <p className="text-gray-500">Nenhuma receita encontrada</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* All Gastos */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-6">Todas as Despesas</h3>
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {gastos.map((gasto) => (
                      <div key={`gasto-${gasto.id}`} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                            <DollarSign className="h-5 w-5 text-red-600" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">{gasto.nome_gasto}</p>
                            <p className="text-sm text-gray-500">{gasto.data_gasto}</p>
                          </div>
                        </div>
                        <p className="font-semibold text-red-600">
                          -{formatCurrency(gasto.valor_gasto)}
                        </p>
                      </div>
                    ))}
                    {gastos.length === 0 && (
                      <div className="text-center py-8">
                        <p className="text-gray-500">Nenhuma despesa encontrada</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Goals Section */}
          {activeSection === 'goals' && (
            <div className="space-y-8">
              <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">Metas Financeiras</h1>
                <p className="text-gray-600">Defina e acompanhe suas metas financeiras</p>
              </div>

              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8 text-center">
                <Target className="w-16 h-16 text-lia-green mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Em Breve</h3>
                <p className="text-gray-600 mb-6">
                  Funcionalidade de metas financeiras em desenvolvimento
                </p>
                <Button className="bg-lia-green hover:bg-emerald-600">
                  Notificar quando estiver pronto
                </Button>
              </div>
            </div>
          )}

          {/* Account Section */}
          {activeSection === 'account' && (
            <div className="space-y-8">
              <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">Minha Conta</h1>
                <p className="text-gray-600">Gerencie suas informações pessoais</p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Profile Info */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-6">Informações Pessoais</h3>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Nome</label>
                      <p className="text-gray-900">{user?.nome || 'Não informado'}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                      <p className="text-gray-900">{user?.email}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Número</label>
                      <p className="text-gray-900">{user?.numero || 'Não informado'}</p>
                    </div>
                  </div>
                </div>

                {/* Account Actions */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-6">Ações da Conta</h3>
                  <div className="space-y-4">
                    <Button variant="outline" className="w-full justify-start gap-2">
                      <Settings className="w-4 h-4" />
                      Configurações
                    </Button>
                    <Button variant="outline" className="w-full justify-start gap-2">
                      <Bell className="w-4 h-4" />
                      Notificações
                    </Button>
                    <Button
                      variant="destructive"
                      className="w-full justify-start gap-2"
                      onClick={handleLogout}
                    >
                      <LogOut className="w-4 h-4" />
                      Sair da Conta
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

export default Dashboard;
