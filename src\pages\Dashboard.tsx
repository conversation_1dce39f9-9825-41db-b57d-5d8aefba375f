
import { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuthContext } from "@/contexts/AuthContext";
import { useFinancialData } from "@/hooks/useFinancialData";
import { 
  MessageCircle, 
  User, 
  Settings, 
  LogOut, 
  PieChart, 
  TrendingUp, 
  DollarSign,
  Calendar,
  Bell,
  Search,
  Wallet,
  ArrowUpCircle,
  ArrowDownCircle
} from "lucide-react";
import { Button } from "@/components/ui/button";

const Dashboard = () => {
  const { user, signOut } = useAuthContext();
  const navigate = useNavigate();

  const {
    loading,
    error,
    totalSaldo,
    totalEntradas,
    totalGastos,
    monthlyTotalEntradas,
    monthlyTotalGastos,
    recentEntradas,
    recentGastos,
    saldos
  } = useFinancialData();

  const handleLogout = async () => {
    await signOut();
    navigate("/");
  };

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  // Show simple error message if there's an error
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-8 text-center">
          <h2 className="text-xl font-bold text-gray-900 mb-4">
            Erro ao carregar dados
          </h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <Button
            onClick={() => window.location.reload()}
            className="w-full bg-lia-green hover:bg-emerald-600"
          >
            Recarregar Página
          </Button>
        </div>
      </div>
    );
  }

  const stats = [
    {
      title: "Receitas do Mês",
      value: formatCurrency(monthlyTotalEntradas),
      change: loading ? "..." : `${recentEntradas.length} entradas`,
      icon: TrendingUp,
      color: "text-green-600"
    },
    {
      title: "Despesas do Mês",
      value: formatCurrency(monthlyTotalGastos),
      change: loading ? "..." : `${recentGastos.length} gastos`,
      icon: DollarSign,
      color: "text-red-600"
    },
    {
      title: "Saldo Total",
      value: formatCurrency(totalSaldo),
      change: loading ? "..." : `${saldos.length} contas`,
      icon: PieChart,
      color: "text-blue-600"
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <Link to="/" className="flex items-center gap-3 group">
              <div className="relative">
                <div className="absolute inset-0 bg-lia-green/20 rounded-full blur-sm group-hover:blur-md transition-all duration-300"></div>
                <MessageCircle className="relative w-8 h-8 text-lia-green group-hover:scale-110 transition-transform duration-300" />
              </div>
              <span className="text-xl font-black tracking-tight text-gray-900 group-hover:text-lia-green transition-colors duration-300">
                LIA
              </span>
            </Link>

            {/* Search */}
            <div className="flex-1 max-w-md mx-8">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Buscar transações..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lia-green focus:border-lia-green"
                />
              </div>
            </div>

            {/* User Menu */}
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="icon">
                <Bell className="h-5 w-5" />
              </Button>
              <div className="flex items-center gap-3">
                <div className="text-right hidden sm:block">
                  <p className="text-sm font-medium text-gray-900">
                    {user?.nome || (user?.numero ? `Usuário #${user.numero}` : 'Usuário')}
                  </p>
                  <p className="text-xs text-gray-500">{user?.email}</p>
                </div>
                <Button variant="ghost" size="icon">
                  <User className="h-5 w-5" />
                </Button>
                <Button variant="ghost" size="icon" onClick={handleLogout}>
                  <LogOut className="h-5 w-5" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Bem-vindo de volta, {user?.nome || (user?.numero ? `#${user.numero}` : 'usuário')}! 👋
          </h1>
          <p className="text-gray-600">
            Aqui está um resumo das suas finanças hoje.
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {stats.map((stat, index) => (
            <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  <p className={`text-sm font-medium ${stat.color}`}>{stat.change} vs mês passado</p>
                </div>
                <div className={`p-3 rounded-full bg-gray-50`}>
                  <stat.icon className={`h-6 w-6 ${stat.color}`} />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Recent Transactions */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-gray-900">Transações Recentes</h2>
              <Button variant="outline" size="sm">Ver Todas</Button>
            </div>
            <div className="space-y-4">
              {loading ? (
                <div className="text-center py-4">
                  <p className="text-gray-500">Carregando transações...</p>
                </div>
              ) : error ? (
                <div className="text-center py-4">
                  <p className="text-red-500">Erro ao carregar dados: {error}</p>
                </div>
              ) : (
                <>
                  {/* Recent Entradas */}
                  {recentEntradas.map((entrada) => (
                    <div key={`entrada-${entrada.id}`} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                          <TrendingUp className="h-5 w-5 text-green-600" />
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{entrada.nome_entrada}</p>
                          <p className="text-sm text-gray-500">Entrada • {entrada.data_entrada}</p>
                        </div>
                      </div>
                      <p className="font-semibold text-green-600">
                        +{formatCurrency(entrada.valor_entrada)}
                      </p>
                    </div>
                  ))}

                  {/* Recent Gastos */}
                  {recentGastos.map((gasto) => (
                    <div key={`gasto-${gasto.id}`} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                          <DollarSign className="h-5 w-5 text-red-600" />
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{gasto.nome_gasto}</p>
                          <p className="text-sm text-gray-500">Gasto • {gasto.data_gasto}</p>
                        </div>
                      </div>
                      <p className="font-semibold text-red-600">
                        -{formatCurrency(gasto.valor_gasto)}
                      </p>
                    </div>
                  ))}

                  {recentEntradas.length === 0 && recentGastos.length === 0 && (
                    <div className="text-center py-8">
                      <p className="text-gray-500">Nenhuma transação encontrada</p>
                      <p className="text-sm text-gray-400 mt-1">Suas transações aparecerão aqui</p>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Ações Rápidas</h2>
            <div className="grid grid-cols-2 gap-4">
              <Button className="h-20 flex-col gap-2 bg-gradient-to-r from-lia-green to-emerald-500 hover:from-emerald-600 hover:to-lia-green">
                <Wallet className="h-5 w-5" />
                Novo Saldo
              </Button>
              <Button variant="outline" className="h-20 flex-col gap-2">
                <ArrowUpCircle className="h-5 w-5" />
                Nova Entrada
              </Button>
              <Button variant="outline" className="h-20 flex-col gap-2">
                <ArrowDownCircle className="h-5 w-5" />
                Novo Gasto
              </Button>
              <Button variant="outline" className="h-20 flex-col gap-2">
                <Bell className="h-5 w-5" />
                Novo Lembrete
              </Button>
            </div>
          </div>
        </div>

        {/* Chat with LIA */}
        <div className="bg-gradient-to-r from-lia-green to-emerald-500 rounded-xl p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold mb-2">Converse com a LIA</h2>
              <p className="text-green-100">
                Tire suas dúvidas financeiras e receba conselhos personalizados
              </p>
            </div>
            <Button variant="secondary" className="bg-white text-lia-green hover:bg-gray-100">
              <MessageCircle className="h-4 w-4 mr-2" />
              Iniciar Chat
            </Button>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
