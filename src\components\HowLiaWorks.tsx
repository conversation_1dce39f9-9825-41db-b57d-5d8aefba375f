
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";

const HowLiaWorks = () => (
  <section className="py-24 bg-gradient-to-br from-gray-50 to-emerald-50/30">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
        {/* Content */}
        <div className="space-y-8">
          <div>
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-lia-green/10 rounded-full mb-6">
              <Brain className="w-4 h-4 text-lia-green" />
              <span className="text-sm font-semibold text-lia-green">INTELIGÊNCIA ARTIFICIAL</span>
            </div>
            <h2 className="text-4xl sm:text-5xl font-black text-gray-900 mb-6 leading-tight">
              Organizar sua vida financeira 
              <span className="block bg-gradient-to-r from-lia-green to-emerald-500 bg-clip-text text-transparent">
                nunca foi tão fácil
              </span>
            </h2>
            <p className="text-xl text-gray-600 leading-relaxed mb-8">
              Imagine ter uma assistente inteligente que entende seus recados, registra tudo e ainda te lembra dos compromissos financeiros sem que você precise se preocupar. 
              <span className="font-semibold text-lia-green"> Isso é a Lia, do WhatsApp diretamente para sua vida!</span>
            </p>
          </div>

          {/* Features */}
          <div className="space-y-6">
            {[
              {
                icon: <MessageCircle className="w-6 h-6" />,
                title: "Respostas rápidas, humanizadas e sem enrolação",
                desc: "IA treinada para entender seu contexto financeiro e responder de forma natural"
              },
              {
                icon: <CalendarCheck className="w-6 h-6" />,
                title: "Lembretes automáticos de contas e metas",
                desc: "Nunca mais esqueça um vencimento ou perca o foco nas suas metas financeiras"
              },
              {
                icon: <Sparkles className="w-6 h-6" />,
                title: "Insights personalizados sobre seus gastos",
                desc: "Descubra padrões nos seus gastos e receba dicas para otimizar seu dinheiro"
              }
            ].map((feature, idx) => (
              <div key={idx} className="flex items-start gap-4 group">
                <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-lia-green to-emerald-500 text-white rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  {feature.icon}
                </div>
                <div>
                  <h4 className="font-bold text-gray-900 mb-2 group-hover:text-lia-green transition-colors duration-300">
                    {feature.title}
                  </h4>
                  <p className="text-gray-600">
                    {feature.desc}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Visual */}
        <div className="relative">
          {/* Background Blur Elements */}
          <div className="absolute inset-0">
            <div className="absolute top-0 right-0 w-72 h-72 bg-lia-green/10 rounded-full blur-3xl"></div>
            <div className="absolute bottom-0 left-0 w-64 h-64 bg-emerald-300/10 rounded-full blur-3xl"></div>
          </div>
          
          {/* Phone Mockup */}
          <div className="relative bg-white rounded-3xl shadow-2xl p-6 max-w-sm mx-auto">
            <div className="bg-gray-900 rounded-2xl p-1">
              <div className="bg-white rounded-xl p-4 space-y-4">
                {/* Header */}
                <div className="flex items-center gap-3 pb-3 border-b border-gray-100">
                  <div className="w-10 h-10 bg-gradient-to-br from-lia-green to-emerald-500 rounded-full flex items-center justify-center">
                    <MessageCircle className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h4 className="font-bold text-gray-900">Lia</h4>
                    <span className="text-xs text-green-500">● online</span>
                  </div>
                </div>
                
                {/* Messages */}
                <div className="space-y-3">
                  <div className="bg-gray-100 rounded-2xl rounded-tl-sm p-3 max-w-[80%]">
                    <p className="text-sm text-gray-800">Oi! Gastei R$ 45 no almoço hoje</p>
                  </div>
                  <div className="bg-lia-green text-white rounded-2xl rounded-tr-sm p-3 max-w-[80%] ml-auto">
                    <p className="text-sm">Registrado! ✅ Você já gastou R$ 180 com alimentação este mês. Quer definir um limite?</p>
                  </div>
                  <div className="bg-gray-100 rounded-2xl rounded-tl-sm p-3 max-w-[80%]">
                    <p className="text-sm text-gray-800">Sim, R$ 300 por mês</p>
                  </div>
                  <div className="bg-lia-green text-white rounded-2xl rounded-tr-sm p-3 max-w-[80%] ml-auto">
                    <p className="text-sm">Perfeito! Meta criada. Te aviso quando chegar perto do limite 😊</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Floating Elements */}
          <div className="absolute -top-6 -left-6 w-12 h-12 bg-gradient-to-br from-lia-green to-emerald-500 rounded-xl rotate-12 animate-bounce shadow-lg"></div>
          <div className="absolute -bottom-6 -right-6 w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full animate-bounce delay-300 shadow-lg"></div>
        </div>
      </div>
    </div>
  </section>
);

export default HowLiaWorks;
