<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#gradient)"/>
  
  <!-- Gradient Definition -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Main Icon -->
  <g transform="translate(100, 200)">
    <rect width="80" height="80" rx="20" fill="#10B981"/>
    <path d="M20 35C20 32.2386 22.2386 30 25 30H35C37.7614 30 40 32.2386 40 35V45C40 47.7614 37.7614 50 35 50H30L25 55V50C22.2386 50 20 47.7614 20 45V35Z" fill="white"/>
    <path d="M45 40C45 37.2386 47.2386 35 50 35H55C57.7614 35 60 37.2386 60 40V50C60 52.7614 57.7614 55 55 55H52.5L50 57.5V55C47.2386 55 45 52.7614 45 50V40Z" fill="white" opacity="0.8"/>
    <circle cx="30" cy="40" r="2.5" fill="#10B981"/>
    <circle cx="52.5" cy="45" r="1.25" fill="#10B981"/>
  </g>
  
  <!-- Title -->
  <text x="220" y="280" font-family="Inter, sans-serif" font-size="72" font-weight="900" fill="#1f2937">LIA</text>
  
  <!-- Subtitle -->
  <text x="220" y="330" font-family="Inter, sans-serif" font-size="36" font-weight="600" fill="#10B981">Assistente Financeira Inteligente</text>
  
  <!-- Description -->
  <text x="220" y="380" font-family="Inter, sans-serif" font-size="24" font-weight="400" fill="#6b7280">Organize suas finanças diretamente pelo WhatsApp</text>
  <text x="220" y="420" font-family="Inter, sans-serif" font-size="24" font-weight="400" fill="#6b7280">Simples • Seguro • Eficiente</text>
  
  <!-- Decorative Elements -->
  <circle cx="950" cy="150" r="60" fill="#10B981" opacity="0.1"/>
  <circle cx="1050" cy="400" r="40" fill="#10B981" opacity="0.15"/>
  <circle cx="850" cy="500" r="30" fill="#10B981" opacity="0.1"/>
</svg>
