
const AvisoIsencao = () => (
  <div className="min-h-screen bg-white">
    <div className="max-w-4xl mx-auto px-4 py-16">
      <h1 className="text-3xl font-bold text-black mb-8">AVISO DE ISENÇÃO DE RESPONSABILIDADE E RISCOS</h1>
      <p className="text-lg font-semibold text-lia-green mb-8">LIA – ASSISTENTE FINANCEIRA</p>
      
      <div className="space-y-8 text-gray-700">
        <section>
          <h2 className="text-xl font-bold text-black mb-4">1. RISCOS INERENTES AO USO</h2>
          <p>O usuário reconhece que, embora adotadas medidas técnicas e administrativas para a proteção de dados pessoais e sensíveis, existem riscos inerentes ao ambiente digital, incluindo falhas técnicas, incidentes de segurança e acessos não autorizados.</p>
        </section>

        <section>
          <h2 className="text-xl font-bold text-black mb-4">2. LIMITAÇÃO DE RESPONSABILIDADE</h2>
          <p className="mb-2">O responsável pela ferramenta não se responsabiliza:</p>
          <div className="space-y-2 ml-4">
            <p><strong>I –</strong> Por decisões financeiras, perdas, danos ou prejuízos de qualquer natureza resultantes da utilização das informações disponibilizadas pela ferramenta;</p>
            <p><strong>II –</strong> Por incidentes de segurança ou falhas operacionais alheias ao controle direto e exclusivo do controlador;</p>
            <p><strong>III –</strong> Pelo conteúdo enviado pelo próprio usuário, de exclusiva responsabilidade deste.</p>
          </div>
        </section>

        <section>
          <h2 className="text-xl font-bold text-black mb-4">3. CONSENTIMENTO E ISENÇÃO FORMAL</h2>
          <p>O usuário consente expressamente com o tratamento de seus dados, inclusive dados sensíveis e conteúdos multimídia, nos termos estabelecidos, e declara ciência dos riscos inerentes à utilização do serviço, isentando o desenvolvedor Matheus Rodrigo da Silva Santos de responsabilidade por fatos não atribuíveis a sua culpa exclusiva.</p>
        </section>
      </div>
    </div>
  </div>
);

export default AvisoIsencao;
